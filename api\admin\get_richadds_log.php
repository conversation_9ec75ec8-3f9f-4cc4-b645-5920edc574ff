<?php
header('Content-Type: application/json');

$logFile = __DIR__ . '/../../database/richadds_success_log.csv';

if (!file_exists($logFile)) {
    echo json_encode(['success' => true, 'data' => [], 'pagination' => ['page' => 1, 'total' => 0, 'totalPages' => 1]]);
    exit;
}

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$search = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$data = array_map('str_getcsv', file($logFile));
$header = array_shift($data);

// Функция для парсинга времени из CSV (формат HH:MM:SS)
if (!function_exists('parseTimeFromCsv')) {
    function parseTimeFromCsv($timeStr) {
        // Время в формате HH:MM:SS, добавляем сегодняшнюю дату для сравнения
        $today = date('Y-m-d');
        return strtotime($today . ' ' . $timeStr);
    }
}

// Функция для преобразования технических названий платформ в читаемый вид
if (!function_exists('formatPlatform')) {
    function formatPlatform($platform) {
        $platformMap = [
            'Linux armv7l' => 'Android (ARM 32-bit)',
            'Linux aarch64' => 'Android (ARM 64-bit)',
            'Linux x86_64' => 'Linux (64-bit)',
            'Linux i686' => 'Linux (32-bit)',
            'Win32' => 'Windows',
            'MacIntel' => 'macOS (Intel)',
            'MacPPC' => 'macOS (PowerPC)',
            'iPhone' => 'iOS (iPhone)',
            'iPad' => 'iOS (iPad)',
            'iPod' => 'iOS (iPod)'
        ];

        return isset($platformMap[$platform]) ? $platformMap[$platform] : $platform;
    }
}

// Фильтрация по поиску
if ($search) {
    $data = array_filter($data, function($row) use ($search) {
        return stripos($row[0], $search) !== false || stripos($row[1], $search) !== false;
    });
}

// Фильтрация по датам
if ($dateFrom || $dateTo) {
    $data = array_filter($data, function($row) use ($dateFrom, $dateTo) {
        if (count($row) < 7) return true; // Пропускаем некорректные строки

        $timeStr = $row[6]; // Время в последней колонке
        $recordTime = parseTimeFromCsv($timeStr);

        if ($dateFrom) {
            $fromTimestamp = strtotime($dateFrom . ' 00:00:00');
            if ($recordTime < $fromTimestamp) return false;
        }

        if ($dateTo) {
            $toTimestamp = strtotime($dateTo . ' 23:59:59');
            if ($recordTime > $toTimestamp) return false;
        }

        return true;
    });
}

// Пагинация
$total = count($data);
$totalPages = ceil($total / $limit);
$offset = ($page - 1) * $limit;
$data = array_slice($data, $offset, $limit);

// Форматируем платформы в данных
$formattedData = array_map(function($row) {
    if (count($row) >= 4) {
        $row[3] = formatPlatform($row[3]); // Форматируем платформу
    }
    return $row;
}, $data);

$response = [
    'success' => true,
    'data' => $formattedData,
    'pagination' => [
        'page' => $page,
        'total' => $total,
        'totalPages' => $totalPages
    ]
];

echo json_encode($response);
