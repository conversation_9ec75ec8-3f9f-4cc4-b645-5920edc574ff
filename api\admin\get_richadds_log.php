<?php
header('Content-Type: application/json');

$logFile = __DIR__ . '/../../database/richadds_success_log.csv';

if (!file_exists($logFile)) {
    echo json_encode(['success' => true, 'data' => [], 'pagination' => ['page' => 1, 'total' => 0, 'totalPages' => 1]]);
    exit;
}

$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$search = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$data = array_map('str_getcsv', file($logFile));
$header = array_shift($data);

// Фильтрация
if ($search) {
    $data = array_filter($data, function($row) use ($search) {
        return stripos($row[0], $search) !== false || stripos($row[1], $search) !== false;
    });
}

// Пагинация
$total = count($data);
$totalPages = ceil($total / $limit);
$offset = ($page - 1) * $limit;
$data = array_slice($data, $offset, $limit);

$response = [
    'success' => true,
    'data' => $data,
    'pagination' => [
        'page' => $page,
        'total' => $total,
        'totalPages' => $totalPages
    ]
];

echo json_encode($response);
