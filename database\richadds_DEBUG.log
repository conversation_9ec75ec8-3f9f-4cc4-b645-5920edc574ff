2025-07-08 13:03:52 - --- Request Started ---
2025-07-08 13:03:52 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:03:52 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:03:52 - Prepared data for CSV: 5088027497,51.158.62.102,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:03:52
2025-07-08 13:03:52 - fopen() successful.
2025-07-08 13:03:52 - fputcsv() successful. Bytes written: 101
2025-07-08 13:03:52 - fclose() successful.
2025-07-08 13:03:52 - Sending response: success='1', message='Log successful'
2025-07-08 13:03:55 - --- Request Started ---
2025-07-08 13:03:55 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:03:55 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:03:55 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:03:55
2025-07-08 13:03:55 - fopen() successful.
2025-07-08 13:03:55 - fputcsv() successful. Bytes written: 100
2025-07-08 13:03:55 - fclose() successful.
2025-07-08 13:03:55 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:21 - --- Request Started ---
2025-07-08 13:04:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:21 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:21 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:04:21
2025-07-08 13:04:21 - fopen() successful.
2025-07-08 13:04:21 - fputcsv() successful. Bytes written: 100
2025-07-08 13:04:21 - fclose() successful.
2025-07-08 13:04:21 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:30 - --- Request Started ---
2025-07-08 13:04:30 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:30 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:30 - Prepared data for CSV: 5088027497,51.158.62.102,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:04:30
2025-07-08 13:04:30 - fopen() successful.
2025-07-08 13:04:30 - fputcsv() successful. Bytes written: 101
2025-07-08 13:04:30 - fclose() successful.
2025-07-08 13:04:30 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:51 - --- Request Started ---
2025-07-08 13:04:51 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:51 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:51 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:04:51
2025-07-08 13:04:51 - fopen() successful.
2025-07-08 13:04:51 - fputcsv() successful. Bytes written: 100
2025-07-08 13:04:51 - fclose() successful.
2025-07-08 13:04:51 - Sending response: success='1', message='Log successful'
2025-07-08 13:05:34 - --- Request Started ---
2025-07-08 13:05:34 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:05:34 - Received data: {"telegramUserId":5880288830,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x806","deviceManufacturer":"Google Inc."}
2025-07-08 13:05:34 - Prepared data for CSV: 5880288830,176.15.197.57,Linux aarch64,Linux aarch64,360x806,Google Inc.,2025-07-08 13:05:34
2025-07-08 13:05:34 - fopen() successful.
2025-07-08 13:05:34 - fputcsv() successful. Bytes written: 101
2025-07-08 13:05:34 - fclose() successful.
2025-07-08 13:05:34 - Sending response: success='1', message='Log successful'
2025-07-08 13:05:47 - --- Request Started ---
2025-07-08 13:05:47 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:05:47 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:05:47 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:05:47
2025-07-08 13:05:47 - fopen() successful.
2025-07-08 13:05:47 - fputcsv() successful. Bytes written: 100
2025-07-08 13:05:47 - fclose() successful.
2025-07-08 13:05:47 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:13 - --- Request Started ---
2025-07-08 13:06:13 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:13 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:13 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:06:13
2025-07-08 13:06:13 - fopen() successful.
2025-07-08 13:06:13 - fputcsv() successful. Bytes written: 100
2025-07-08 13:06:13 - fclose() successful.
2025-07-08 13:06:13 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:19 - --- Request Started ---
2025-07-08 13:06:19 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:19 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:19 - Prepared data for CSV: 5088027497,146.70.42.202,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:06:19
2025-07-08 13:06:19 - fopen() successful.
2025-07-08 13:06:19 - fputcsv() successful. Bytes written: 101
2025-07-08 13:06:19 - fclose() successful.
2025-07-08 13:06:19 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:58 - --- Request Started ---
2025-07-08 13:06:58 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:58 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:58 - Prepared data for CSV: 5088027497,146.70.42.202,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:06:58
2025-07-08 13:06:58 - fopen() successful.
2025-07-08 13:06:58 - fputcsv() successful. Bytes written: 101
2025-07-08 13:06:58 - fclose() successful.
2025-07-08 13:06:58 - Sending response: success='1', message='Log successful'
