2025-07-08 13:03:52 - --- Request Started ---
2025-07-08 13:03:52 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:03:52 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:03:52 - Prepared data for CSV: 5088027497,51.158.62.102,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:03:52
2025-07-08 13:03:52 - fopen() successful.
2025-07-08 13:03:52 - fputcsv() successful. Bytes written: 101
2025-07-08 13:03:52 - fclose() successful.
2025-07-08 13:03:52 - Sending response: success='1', message='Log successful'
2025-07-08 13:03:55 - --- Request Started ---
2025-07-08 13:03:55 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:03:55 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:03:55 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:03:55
2025-07-08 13:03:55 - fopen() successful.
2025-07-08 13:03:55 - fputcsv() successful. Bytes written: 100
2025-07-08 13:03:55 - fclose() successful.
2025-07-08 13:03:55 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:21 - --- Request Started ---
2025-07-08 13:04:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:21 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:21 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:04:21
2025-07-08 13:04:21 - fopen() successful.
2025-07-08 13:04:21 - fputcsv() successful. Bytes written: 100
2025-07-08 13:04:21 - fclose() successful.
2025-07-08 13:04:21 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:30 - --- Request Started ---
2025-07-08 13:04:30 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:30 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:30 - Prepared data for CSV: 5088027497,51.158.62.102,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:04:30
2025-07-08 13:04:30 - fopen() successful.
2025-07-08 13:04:30 - fputcsv() successful. Bytes written: 101
2025-07-08 13:04:30 - fclose() successful.
2025-07-08 13:04:30 - Sending response: success='1', message='Log successful'
2025-07-08 13:04:51 - --- Request Started ---
2025-07-08 13:04:51 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:04:51 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:04:51 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:04:51
2025-07-08 13:04:51 - fopen() successful.
2025-07-08 13:04:51 - fputcsv() successful. Bytes written: 100
2025-07-08 13:04:51 - fclose() successful.
2025-07-08 13:04:51 - Sending response: success='1', message='Log successful'
2025-07-08 13:05:34 - --- Request Started ---
2025-07-08 13:05:34 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:05:34 - Received data: {"telegramUserId":5880288830,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x806","deviceManufacturer":"Google Inc."}
2025-07-08 13:05:34 - Prepared data for CSV: 5880288830,176.15.197.57,Linux aarch64,Linux aarch64,360x806,Google Inc.,2025-07-08 13:05:34
2025-07-08 13:05:34 - fopen() successful.
2025-07-08 13:05:34 - fputcsv() successful. Bytes written: 101
2025-07-08 13:05:34 - fclose() successful.
2025-07-08 13:05:34 - Sending response: success='1', message='Log successful'
2025-07-08 13:05:47 - --- Request Started ---
2025-07-08 13:05:47 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:05:47 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:05:47 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:05:47
2025-07-08 13:05:47 - fopen() successful.
2025-07-08 13:05:47 - fputcsv() successful. Bytes written: 100
2025-07-08 13:05:47 - fclose() successful.
2025-07-08 13:05:47 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:13 - --- Request Started ---
2025-07-08 13:06:13 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:13 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:13 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:06:13
2025-07-08 13:06:13 - fopen() successful.
2025-07-08 13:06:13 - fputcsv() successful. Bytes written: 100
2025-07-08 13:06:13 - fclose() successful.
2025-07-08 13:06:13 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:19 - --- Request Started ---
2025-07-08 13:06:19 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:19 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:19 - Prepared data for CSV: 5088027497,146.70.42.202,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:06:19
2025-07-08 13:06:19 - fopen() successful.
2025-07-08 13:06:19 - fputcsv() successful. Bytes written: 101
2025-07-08 13:06:19 - fclose() successful.
2025-07-08 13:06:19 - Sending response: success='1', message='Log successful'
2025-07-08 13:06:58 - --- Request Started ---
2025-07-08 13:06:58 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:06:58 - Received data: {"telegramUserId":5088027497,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x800","deviceManufacturer":"Google Inc."}
2025-07-08 13:06:58 - Prepared data for CSV: 5088027497,146.70.42.202,Linux aarch64,Linux aarch64,360x800,Google Inc.,2025-07-08 13:06:58
2025-07-08 13:06:58 - fopen() successful.
2025-07-08 13:06:58 - fputcsv() successful. Bytes written: 101
2025-07-08 13:06:58 - fclose() successful.
2025-07-08 13:06:58 - Sending response: success='1', message='Log successful'
2025-07-08 13:09:25 - --- Request Started ---
2025-07-08 13:09:25 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:09:25 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:09:25 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:09:25
2025-07-08 13:09:25 - fopen() successful.
2025-07-08 13:09:25 - fputcsv() successful. Bytes written: 100
2025-07-08 13:09:25 - fclose() successful.
2025-07-08 13:09:25 - Sending response: success='1', message='Log successful'
2025-07-08 13:10:53 - --- Request Started ---
2025-07-08 13:10:53 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:10:53 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:10:53 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:10:53
2025-07-08 13:10:53 - fopen() successful.
2025-07-08 13:10:53 - fputcsv() successful. Bytes written: 100
2025-07-08 13:10:53 - fclose() successful.
2025-07-08 13:10:53 - Sending response: success='1', message='Log successful'
2025-07-08 13:11:10 - --- Request Started ---
2025-07-08 13:11:10 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:11:10 - Received data: {"telegramUserId":7705114667,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:11:10 - Prepared data for CSV: 7705114667,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:11:10
2025-07-08 13:11:10 - fopen() successful.
2025-07-08 13:11:10 - fputcsv() successful. Bytes written: 100
2025-07-08 13:11:10 - fclose() successful.
2025-07-08 13:11:10 - Sending response: success='1', message='Log successful'
2025-07-08 13:11:18 - --- Request Started ---
2025-07-08 13:11:18 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:11:18 - Received data: {"telegramUserId":6042251387,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"393x851","deviceManufacturer":"Google Inc."}
2025-07-08 13:11:18 - Prepared data for CSV: 6042251387,51.68.140.75,Linux aarch64,Linux aarch64,393x851,Google Inc.,2025-07-08 13:11:18
2025-07-08 13:11:18 - fopen() successful.
2025-07-08 13:11:18 - fputcsv() successful. Bytes written: 100
2025-07-08 13:11:18 - fclose() successful.
2025-07-08 13:11:18 - Sending response: success='1', message='Log successful'
2025-07-08 13:11:32 - --- Request Started ---
2025-07-08 13:11:32 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:11:32 - Received data: {"telegramUserId":7705114667,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:11:32 - Prepared data for CSV: 7705114667,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:11:32
2025-07-08 13:11:32 - fopen() successful.
2025-07-08 13:11:32 - fputcsv() successful. Bytes written: 100
2025-07-08 13:11:32 - fclose() successful.
2025-07-08 13:11:32 - Sending response: success='1', message='Log successful'
2025-07-08 13:14:48 - --- Request Started ---
2025-07-08 13:14:48 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:14:48 - Received data: {"telegramUserId":7705114667,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:14:48 - Prepared data for CSV: 7705114667,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:14:48
2025-07-08 13:14:48 - fopen() successful.
2025-07-08 13:14:48 - fputcsv() successful. Bytes written: 100
2025-07-08 13:14:48 - fclose() successful.
2025-07-08 13:14:48 - Sending response: success='1', message='Log successful'
2025-07-08 13:15:27 - --- Request Started ---
2025-07-08 13:15:27 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:15:27 - Received data: {"telegramUserId":7705114667,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:15:27 - Prepared data for CSV: 7705114667,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:15:27
2025-07-08 13:15:27 - fopen() successful.
2025-07-08 13:15:27 - fputcsv() successful. Bytes written: 100
2025-07-08 13:15:27 - fclose() successful.
2025-07-08 13:15:27 - Sending response: success='1', message='Log successful'
2025-07-08 13:18:01 - --- Request Started ---
2025-07-08 13:18:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:18:01 - Received data: {"telegramUserId":7705114667,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:18:01 - Prepared data for CSV: 7705114667,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:18:01
2025-07-08 13:18:01 - fopen() successful.
2025-07-08 13:18:01 - fputcsv() successful. Bytes written: 100
2025-07-08 13:18:01 - fclose() successful.
2025-07-08 13:18:01 - Sending response: success='1', message='Log successful'
2025-07-08 13:24:18 - --- Request Started ---
2025-07-08 13:24:18 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:24:18 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:24:18 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:24:18
2025-07-08 13:24:18 - fopen() successful.
2025-07-08 13:24:18 - fputcsv() successful. Bytes written: 100
2025-07-08 13:24:18 - fclose() successful.
2025-07-08 13:24:18 - Sending response: success='1', message='Log successful'
2025-07-08 13:24:46 - --- Request Started ---
2025-07-08 13:24:46 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:24:46 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:24:46 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:24:46
2025-07-08 13:24:46 - fopen() successful.
2025-07-08 13:24:46 - fputcsv() successful. Bytes written: 100
2025-07-08 13:24:46 - fclose() successful.
2025-07-08 13:24:46 - Sending response: success='1', message='Log successful'
2025-07-08 13:25:12 - --- Request Started ---
2025-07-08 13:25:12 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:25:12 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:25:12 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:25:12
2025-07-08 13:25:12 - fopen() successful.
2025-07-08 13:25:12 - fputcsv() successful. Bytes written: 100
2025-07-08 13:25:12 - fclose() successful.
2025-07-08 13:25:12 - Sending response: success='1', message='Log successful'
2025-07-08 13:25:40 - --- Request Started ---
2025-07-08 13:25:40 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:25:40 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:25:40 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:25:40
2025-07-08 13:25:40 - fopen() successful.
2025-07-08 13:25:40 - fputcsv() successful. Bytes written: 100
2025-07-08 13:25:40 - fclose() successful.
2025-07-08 13:25:40 - Sending response: success='1', message='Log successful'
2025-07-08 13:26:05 - --- Request Started ---
2025-07-08 13:26:05 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:26:05 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:26:05 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:26:05
2025-07-08 13:26:05 - fopen() successful.
2025-07-08 13:26:05 - fputcsv() successful. Bytes written: 100
2025-07-08 13:26:05 - fclose() successful.
2025-07-08 13:26:05 - Sending response: success='1', message='Log successful'
2025-07-08 13:26:11 - --- Request Started ---
2025-07-08 13:26:11 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:26:11 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:26:11 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:26:11
2025-07-08 13:26:11 - fopen() successful.
2025-07-08 13:26:11 - fputcsv() successful. Bytes written: 100
2025-07-08 13:26:11 - fclose() successful.
2025-07-08 13:26:11 - Sending response: success='1', message='Log successful'
2025-07-08 13:26:36 - --- Request Started ---
2025-07-08 13:26:36 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:26:36 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:26:36 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:26:36
2025-07-08 13:26:36 - fopen() successful.
2025-07-08 13:26:36 - fputcsv() successful. Bytes written: 100
2025-07-08 13:26:36 - fclose() successful.
2025-07-08 13:26:36 - Sending response: success='1', message='Log successful'
2025-07-08 13:27:06 - --- Request Started ---
2025-07-08 13:27:06 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:27:06 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:27:06 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:27:06
2025-07-08 13:27:06 - fopen() successful.
2025-07-08 13:27:06 - fputcsv() successful. Bytes written: 100
2025-07-08 13:27:06 - fclose() successful.
2025-07-08 13:27:06 - Sending response: success='1', message='Log successful'
2025-07-08 13:27:30 - --- Request Started ---
2025-07-08 13:27:30 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:27:30 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:27:30 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:27:30
2025-07-08 13:27:30 - fopen() successful.
2025-07-08 13:27:30 - fputcsv() successful. Bytes written: 100
2025-07-08 13:27:30 - fclose() successful.
2025-07-08 13:27:30 - Sending response: success='1', message='Log successful'
2025-07-08 13:27:45 - --- Request Started ---
2025-07-08 13:27:45 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:27:45 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:27:45 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:27:45
2025-07-08 13:27:45 - fopen() successful.
2025-07-08 13:27:45 - fputcsv() successful. Bytes written: 100
2025-07-08 13:27:45 - fclose() successful.
2025-07-08 13:27:45 - Sending response: success='1', message='Log successful'
2025-07-08 13:28:15 - --- Request Started ---
2025-07-08 13:28:15 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:28:15 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:28:15 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:28:15
2025-07-08 13:28:15 - fopen() successful.
2025-07-08 13:28:15 - fputcsv() successful. Bytes written: 100
2025-07-08 13:28:15 - fclose() successful.
2025-07-08 13:28:15 - Sending response: success='1', message='Log successful'
2025-07-08 13:28:38 - --- Request Started ---
2025-07-08 13:28:38 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:28:38 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:28:38 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:28:38
2025-07-08 13:28:38 - fopen() successful.
2025-07-08 13:28:38 - fputcsv() successful. Bytes written: 100
2025-07-08 13:28:38 - fclose() successful.
2025-07-08 13:28:38 - Sending response: success='1', message='Log successful'
2025-07-08 13:28:43 - --- Request Started ---
2025-07-08 13:28:43 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:28:43 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:28:43 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:28:43
2025-07-08 13:28:43 - fopen() successful.
2025-07-08 13:28:43 - fputcsv() successful. Bytes written: 100
2025-07-08 13:28:43 - fclose() successful.
2025-07-08 13:28:43 - Sending response: success='1', message='Log successful'
2025-07-08 13:29:45 - --- Request Started ---
2025-07-08 13:29:45 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:29:45 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:29:45 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:29:45
2025-07-08 13:29:45 - fopen() successful.
2025-07-08 13:29:45 - fputcsv() successful. Bytes written: 100
2025-07-08 13:29:45 - fclose() successful.
2025-07-08 13:29:45 - Sending response: success='1', message='Log successful'
2025-07-08 13:30:38 - --- Request Started ---
2025-07-08 13:30:38 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:30:38 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:30:38 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:30:38
2025-07-08 13:30:38 - fopen() successful.
2025-07-08 13:30:38 - fputcsv() successful. Bytes written: 100
2025-07-08 13:30:38 - fclose() successful.
2025-07-08 13:30:38 - Sending response: success='1', message='Log successful'
2025-07-08 13:31:08 - --- Request Started ---
2025-07-08 13:31:08 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:31:08 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:31:08 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:31:08
2025-07-08 13:31:08 - fopen() successful.
2025-07-08 13:31:08 - fputcsv() successful. Bytes written: 100
2025-07-08 13:31:08 - fclose() successful.
2025-07-08 13:31:08 - Sending response: success='1', message='Log successful'
2025-07-08 13:31:37 - --- Request Started ---
2025-07-08 13:31:37 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:31:37 - Received data: {"telegramUserId":7727592313,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:31:37 - Prepared data for CSV: 7727592313,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:31:37
2025-07-08 13:31:37 - fopen() successful.
2025-07-08 13:31:37 - fputcsv() successful. Bytes written: 100
2025-07-08 13:31:37 - fclose() successful.
2025-07-08 13:31:37 - Sending response: success='1', message='Log successful'
2025-07-08 13:33:01 - --- Request Started ---
2025-07-08 13:33:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:33:01 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:33:01 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:33:01
2025-07-08 13:33:01 - fopen() successful.
2025-07-08 13:33:01 - fputcsv() successful. Bytes written: 100
2025-07-08 13:33:01 - fclose() successful.
2025-07-08 13:33:01 - Sending response: success='1', message='Log successful'
2025-07-08 13:33:47 - --- Request Started ---
2025-07-08 13:33:47 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:33:47 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:33:47 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:33:47
2025-07-08 13:33:47 - fopen() successful.
2025-07-08 13:33:47 - fputcsv() successful. Bytes written: 100
2025-07-08 13:33:47 - fclose() successful.
2025-07-08 13:33:47 - Sending response: success='1', message='Log successful'
2025-07-08 13:34:14 - --- Request Started ---
2025-07-08 13:34:14 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:34:14 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:34:14 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:34:14
2025-07-08 13:34:14 - fopen() successful.
2025-07-08 13:34:14 - fputcsv() successful. Bytes written: 100
2025-07-08 13:34:14 - fclose() successful.
2025-07-08 13:34:14 - Sending response: success='1', message='Log successful'
2025-07-08 13:34:40 - --- Request Started ---
2025-07-08 13:34:40 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:34:40 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:34:40 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:34:40
2025-07-08 13:34:40 - fopen() successful.
2025-07-08 13:34:40 - fputcsv() successful. Bytes written: 100
2025-07-08 13:34:40 - fclose() successful.
2025-07-08 13:34:40 - Sending response: success='1', message='Log successful'
2025-07-08 13:35:23 - --- Request Started ---
2025-07-08 13:35:23 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:35:23 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:35:23 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:35:23
2025-07-08 13:35:23 - fopen() successful.
2025-07-08 13:35:23 - fputcsv() successful. Bytes written: 100
2025-07-08 13:35:23 - fclose() successful.
2025-07-08 13:35:23 - Sending response: success='1', message='Log successful'
2025-07-08 13:35:56 - --- Request Started ---
2025-07-08 13:35:56 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:35:56 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:35:56 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:35:56
2025-07-08 13:35:56 - fopen() successful.
2025-07-08 13:35:56 - fputcsv() successful. Bytes written: 100
2025-07-08 13:35:56 - fclose() successful.
2025-07-08 13:35:56 - Sending response: success='1', message='Log successful'
2025-07-08 13:36:27 - --- Request Started ---
2025-07-08 13:36:27 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:36:27 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:36:27 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:36:27
2025-07-08 13:36:27 - fopen() successful.
2025-07-08 13:36:27 - fputcsv() successful. Bytes written: 100
2025-07-08 13:36:27 - fclose() successful.
2025-07-08 13:36:27 - Sending response: success='1', message='Log successful'
2025-07-08 13:36:55 - --- Request Started ---
2025-07-08 13:36:55 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:36:55 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:36:55 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:36:55
2025-07-08 13:36:55 - fopen() successful.
2025-07-08 13:36:55 - fputcsv() successful. Bytes written: 100
2025-07-08 13:36:55 - fclose() successful.
2025-07-08 13:36:55 - Sending response: success='1', message='Log successful'
2025-07-08 13:37:33 - --- Request Started ---
2025-07-08 13:37:33 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:37:33 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:37:33 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:37:33
2025-07-08 13:37:33 - fopen() successful.
2025-07-08 13:37:33 - fputcsv() successful. Bytes written: 100
2025-07-08 13:37:33 - fclose() successful.
2025-07-08 13:37:33 - Sending response: success='1', message='Log successful'
2025-07-08 13:37:59 - --- Request Started ---
2025-07-08 13:37:59 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:37:59 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:37:59 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:37:59
2025-07-08 13:37:59 - fopen() successful.
2025-07-08 13:37:59 - fputcsv() successful. Bytes written: 100
2025-07-08 13:37:59 - fclose() successful.
2025-07-08 13:37:59 - Sending response: success='1', message='Log successful'
2025-07-08 13:38:21 - --- Request Started ---
2025-07-08 13:38:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:38:21 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:38:21 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:38:21
2025-07-08 13:38:21 - fopen() successful.
2025-07-08 13:38:21 - fputcsv() successful. Bytes written: 100
2025-07-08 13:38:21 - fclose() successful.
2025-07-08 13:38:21 - Sending response: success='1', message='Log successful'
2025-07-08 13:38:32 - --- Request Started ---
2025-07-08 13:38:32 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:38:32 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:38:32 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:38:32
2025-07-08 13:38:32 - fopen() successful.
2025-07-08 13:38:32 - fputcsv() successful. Bytes written: 100
2025-07-08 13:38:32 - fclose() successful.
2025-07-08 13:38:32 - Sending response: success='1', message='Log successful'
2025-07-08 13:39:00 - --- Request Started ---
2025-07-08 13:39:00 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:39:00 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:39:00 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:39:00
2025-07-08 13:39:00 - fopen() successful.
2025-07-08 13:39:00 - fputcsv() successful. Bytes written: 100
2025-07-08 13:39:00 - fclose() successful.
2025-07-08 13:39:00 - Sending response: success='1', message='Log successful'
2025-07-08 13:39:26 - --- Request Started ---
2025-07-08 13:39:26 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:39:26 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:39:26 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:39:26
2025-07-08 13:39:26 - fopen() successful.
2025-07-08 13:39:26 - fputcsv() successful. Bytes written: 100
2025-07-08 13:39:26 - fclose() successful.
2025-07-08 13:39:26 - Sending response: success='1', message='Log successful'
2025-07-08 13:39:55 - --- Request Started ---
2025-07-08 13:39:55 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:39:55 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:39:55 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:39:55
2025-07-08 13:39:55 - fopen() successful.
2025-07-08 13:39:55 - fputcsv() successful. Bytes written: 100
2025-07-08 13:39:55 - fclose() successful.
2025-07-08 13:39:55 - Sending response: success='1', message='Log successful'
2025-07-08 13:40:44 - --- Request Started ---
2025-07-08 13:40:44 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:40:44 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:40:44 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:40:44
2025-07-08 13:40:44 - fopen() successful.
2025-07-08 13:40:44 - fputcsv() successful. Bytes written: 100
2025-07-08 13:40:44 - fclose() successful.
2025-07-08 13:40:44 - Sending response: success='1', message='Log successful'
2025-07-08 13:41:17 - --- Request Started ---
2025-07-08 13:41:17 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:41:17 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:41:17 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:41:17
2025-07-08 13:41:17 - fopen() successful.
2025-07-08 13:41:17 - fputcsv() successful. Bytes written: 100
2025-07-08 13:41:17 - fclose() successful.
2025-07-08 13:41:17 - Sending response: success='1', message='Log successful'
2025-07-08 13:41:43 - --- Request Started ---
2025-07-08 13:41:43 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:41:43 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:41:43 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:41:43
2025-07-08 13:41:43 - fopen() successful.
2025-07-08 13:41:43 - fputcsv() successful. Bytes written: 100
2025-07-08 13:41:43 - fclose() successful.
2025-07-08 13:41:43 - Sending response: success='1', message='Log successful'
2025-07-08 13:42:31 - --- Request Started ---
2025-07-08 13:42:31 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:42:31 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:42:31 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:42:31
2025-07-08 13:42:31 - fopen() successful.
2025-07-08 13:42:31 - fputcsv() successful. Bytes written: 100
2025-07-08 13:42:31 - fclose() successful.
2025-07-08 13:42:31 - Sending response: success='1', message='Log successful'
2025-07-08 13:43:22 - --- Request Started ---
2025-07-08 13:43:22 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:43:22 - Received data: {"telegramUserId":7455313783,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:43:22 - Prepared data for CSV: 7455313783,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:43:22
2025-07-08 13:43:22 - fopen() successful.
2025-07-08 13:43:22 - fputcsv() successful. Bytes written: 100
2025-07-08 13:43:22 - fclose() successful.
2025-07-08 13:43:22 - Sending response: success='1', message='Log successful'
2025-07-08 13:44:20 - --- Request Started ---
2025-07-08 13:44:20 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:44:20 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:44:20 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:44:20
2025-07-08 13:44:20 - fopen() successful.
2025-07-08 13:44:20 - fputcsv() successful. Bytes written: 100
2025-07-08 13:44:20 - fclose() successful.
2025-07-08 13:44:20 - Sending response: success='1', message='Log successful'
2025-07-08 13:44:49 - --- Request Started ---
2025-07-08 13:44:49 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:44:49 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:44:49 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:44:49
2025-07-08 13:44:49 - fopen() successful.
2025-07-08 13:44:49 - fputcsv() successful. Bytes written: 100
2025-07-08 13:44:49 - fclose() successful.
2025-07-08 13:44:49 - Sending response: success='1', message='Log successful'
2025-07-08 13:45:31 - --- Request Started ---
2025-07-08 13:45:31 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:45:31 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:45:31 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:45:31
2025-07-08 13:45:31 - fopen() successful.
2025-07-08 13:45:31 - fputcsv() successful. Bytes written: 100
2025-07-08 13:45:31 - fclose() successful.
2025-07-08 13:45:31 - Sending response: success='1', message='Log successful'
2025-07-08 13:46:01 - --- Request Started ---
2025-07-08 13:46:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:46:01 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:46:01 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:46:01
2025-07-08 13:46:01 - fopen() successful.
2025-07-08 13:46:01 - fputcsv() successful. Bytes written: 100
2025-07-08 13:46:01 - fclose() successful.
2025-07-08 13:46:01 - Sending response: success='1', message='Log successful'
2025-07-08 13:46:29 - --- Request Started ---
2025-07-08 13:46:29 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:46:29 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:46:29 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:46:29
2025-07-08 13:46:29 - fopen() successful.
2025-07-08 13:46:29 - fputcsv() successful. Bytes written: 100
2025-07-08 13:46:29 - fclose() successful.
2025-07-08 13:46:29 - Sending response: success='1', message='Log successful'
2025-07-08 13:47:09 - --- Request Started ---
2025-07-08 13:47:09 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:47:09 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:47:09 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:47:09
2025-07-08 13:47:09 - fopen() successful.
2025-07-08 13:47:09 - fputcsv() successful. Bytes written: 100
2025-07-08 13:47:09 - fclose() successful.
2025-07-08 13:47:09 - Sending response: success='1', message='Log successful'
2025-07-08 13:47:32 - --- Request Started ---
2025-07-08 13:47:32 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:47:32 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:47:32 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:47:32
2025-07-08 13:47:32 - fopen() successful.
2025-07-08 13:47:32 - fputcsv() successful. Bytes written: 100
2025-07-08 13:47:32 - fclose() successful.
2025-07-08 13:47:32 - Sending response: success='1', message='Log successful'
2025-07-08 13:47:46 - --- Request Started ---
2025-07-08 13:47:46 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:47:46 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:47:46 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:47:46
2025-07-08 13:47:46 - fopen() successful.
2025-07-08 13:47:46 - fputcsv() successful. Bytes written: 100
2025-07-08 13:47:46 - fclose() successful.
2025-07-08 13:47:46 - Sending response: success='1', message='Log successful'
2025-07-08 13:48:08 - --- Request Started ---
2025-07-08 13:48:08 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:48:08 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:48:08 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:48:08
2025-07-08 13:48:08 - fopen() successful.
2025-07-08 13:48:08 - fputcsv() successful. Bytes written: 100
2025-07-08 13:48:08 - fclose() successful.
2025-07-08 13:48:08 - Sending response: success='1', message='Log successful'
2025-07-08 13:48:15 - --- Request Started ---
2025-07-08 13:48:15 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:48:15 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:48:15 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:48:15
2025-07-08 13:48:15 - fopen() successful.
2025-07-08 13:48:15 - fputcsv() successful. Bytes written: 100
2025-07-08 13:48:15 - fclose() successful.
2025-07-08 13:48:15 - Sending response: success='1', message='Log successful'
2025-07-08 13:48:48 - --- Request Started ---
2025-07-08 13:48:48 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:48:48 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:48:48 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:48:48
2025-07-08 13:48:48 - fopen() successful.
2025-07-08 13:48:48 - fputcsv() successful. Bytes written: 100
2025-07-08 13:48:48 - fclose() successful.
2025-07-08 13:48:48 - Sending response: success='1', message='Log successful'
2025-07-08 13:49:27 - --- Request Started ---
2025-07-08 13:49:27 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:49:27 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:49:27 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:49:27
2025-07-08 13:49:27 - fopen() successful.
2025-07-08 13:49:27 - fputcsv() successful. Bytes written: 100
2025-07-08 13:49:27 - fclose() successful.
2025-07-08 13:49:27 - Sending response: success='1', message='Log successful'
2025-07-08 13:50:15 - --- Request Started ---
2025-07-08 13:50:15 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:50:15 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:50:15 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:50:15
2025-07-08 13:50:15 - fopen() successful.
2025-07-08 13:50:15 - fputcsv() successful. Bytes written: 100
2025-07-08 13:50:15 - fclose() successful.
2025-07-08 13:50:15 - Sending response: success='1', message='Log successful'
2025-07-08 13:50:56 - --- Request Started ---
2025-07-08 13:50:56 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:50:56 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:50:56 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:50:56
2025-07-08 13:50:56 - fopen() successful.
2025-07-08 13:50:56 - fputcsv() successful. Bytes written: 100
2025-07-08 13:50:56 - fclose() successful.
2025-07-08 13:50:56 - Sending response: success='1', message='Log successful'
2025-07-08 13:52:06 - --- Request Started ---
2025-07-08 13:52:06 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:52:06 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:52:06 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:52:06
2025-07-08 13:52:06 - fopen() successful.
2025-07-08 13:52:06 - fputcsv() successful. Bytes written: 100
2025-07-08 13:52:06 - fclose() successful.
2025-07-08 13:52:06 - Sending response: success='1', message='Log successful'
2025-07-08 13:52:45 - --- Request Started ---
2025-07-08 13:52:45 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:52:45 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:52:45 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:52:45
2025-07-08 13:52:45 - fopen() successful.
2025-07-08 13:52:45 - fputcsv() successful. Bytes written: 100
2025-07-08 13:52:45 - fclose() successful.
2025-07-08 13:52:45 - Sending response: success='1', message='Log successful'
2025-07-08 13:53:24 - --- Request Started ---
2025-07-08 13:53:24 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:53:24 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:53:24 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:53:24
2025-07-08 13:53:24 - fopen() successful.
2025-07-08 13:53:24 - fputcsv() successful. Bytes written: 100
2025-07-08 13:53:24 - fclose() successful.
2025-07-08 13:53:24 - Sending response: success='1', message='Log successful'
2025-07-08 13:54:27 - --- Request Started ---
2025-07-08 13:54:27 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:54:27 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:54:27 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:54:27
2025-07-08 13:54:27 - fopen() successful.
2025-07-08 13:54:27 - fputcsv() successful. Bytes written: 100
2025-07-08 13:54:27 - fclose() successful.
2025-07-08 13:54:27 - Sending response: success='1', message='Log successful'
2025-07-08 13:55:05 - --- Request Started ---
2025-07-08 13:55:05 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:55:05 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:55:05 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:55:05
2025-07-08 13:55:05 - fopen() successful.
2025-07-08 13:55:05 - fputcsv() successful. Bytes written: 100
2025-07-08 13:55:05 - fclose() successful.
2025-07-08 13:55:05 - Sending response: success='1', message='Log successful'
2025-07-08 13:55:56 - --- Request Started ---
2025-07-08 13:55:56 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:55:56 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:55:56 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:55:56
2025-07-08 13:55:56 - fopen() successful.
2025-07-08 13:55:56 - fputcsv() successful. Bytes written: 100
2025-07-08 13:55:56 - fclose() successful.
2025-07-08 13:55:56 - Sending response: success='1', message='Log successful'
2025-07-08 13:56:38 - --- Request Started ---
2025-07-08 13:56:38 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:56:38 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:56:38 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:56:38
2025-07-08 13:56:38 - fopen() successful.
2025-07-08 13:56:38 - fputcsv() successful. Bytes written: 100
2025-07-08 13:56:38 - fclose() successful.
2025-07-08 13:56:38 - Sending response: success='1', message='Log successful'
2025-07-08 13:57:54 - --- Request Started ---
2025-07-08 13:57:54 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 13:57:54 - Received data: {"telegramUserId":7900746233,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 13:57:54 - Prepared data for CSV: 7900746233,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 13:57:54
2025-07-08 13:57:54 - fopen() successful.
2025-07-08 13:57:54 - fputcsv() successful. Bytes written: 100
2025-07-08 13:57:54 - fclose() successful.
2025-07-08 13:57:54 - Sending response: success='1', message='Log successful'
2025-07-08 14:04:00 - --- Request Started ---
2025-07-08 14:04:00 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:04:00 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:04:00 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:04:00
2025-07-08 14:04:00 - fopen() successful.
2025-07-08 14:04:00 - fputcsv() successful. Bytes written: 100
2025-07-08 14:04:00 - fclose() successful.
2025-07-08 14:04:00 - Sending response: success='1', message='Log successful'
2025-07-08 14:04:21 - --- Request Started ---
2025-07-08 14:04:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:04:21 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:04:21 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:04:21
2025-07-08 14:04:21 - fopen() successful.
2025-07-08 14:04:21 - fputcsv() successful. Bytes written: 100
2025-07-08 14:04:21 - fclose() successful.
2025-07-08 14:04:21 - Sending response: success='1', message='Log successful'
2025-07-08 14:04:28 - --- Request Started ---
2025-07-08 14:04:28 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:04:28 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:04:28 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:04:28
2025-07-08 14:04:28 - fopen() successful.
2025-07-08 14:04:28 - fputcsv() successful. Bytes written: 100
2025-07-08 14:04:28 - fclose() successful.
2025-07-08 14:04:28 - Sending response: success='1', message='Log successful'
2025-07-08 14:04:54 - --- Request Started ---
2025-07-08 14:04:54 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:04:54 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:04:54 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:04:54
2025-07-08 14:04:54 - fopen() successful.
2025-07-08 14:04:54 - fputcsv() successful. Bytes written: 100
2025-07-08 14:04:54 - fclose() successful.
2025-07-08 14:04:54 - Sending response: success='1', message='Log successful'
2025-07-08 14:05:23 - --- Request Started ---
2025-07-08 14:05:23 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:05:23 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:05:23 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:05:23
2025-07-08 14:05:23 - fopen() successful.
2025-07-08 14:05:23 - fputcsv() successful. Bytes written: 100
2025-07-08 14:05:23 - fclose() successful.
2025-07-08 14:05:23 - Sending response: success='1', message='Log successful'
2025-07-08 14:05:52 - --- Request Started ---
2025-07-08 14:05:52 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:05:52 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:05:52 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:05:52
2025-07-08 14:05:52 - fopen() successful.
2025-07-08 14:05:52 - fputcsv() successful. Bytes written: 100
2025-07-08 14:05:52 - fclose() successful.
2025-07-08 14:05:52 - Sending response: success='1', message='Log successful'
2025-07-08 14:06:13 - --- Request Started ---
2025-07-08 14:06:13 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:06:13 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:06:13 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:06:13
2025-07-08 14:06:13 - fopen() successful.
2025-07-08 14:06:13 - fputcsv() successful. Bytes written: 100
2025-07-08 14:06:13 - fclose() successful.
2025-07-08 14:06:13 - Sending response: success='1', message='Log successful'
2025-07-08 14:06:20 - --- Request Started ---
2025-07-08 14:06:20 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:06:20 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:06:20 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:06:20
2025-07-08 14:06:20 - fopen() successful.
2025-07-08 14:06:20 - fputcsv() successful. Bytes written: 100
2025-07-08 14:06:20 - fclose() successful.
2025-07-08 14:06:20 - Sending response: success='1', message='Log successful'
2025-07-08 14:06:50 - --- Request Started ---
2025-07-08 14:06:50 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:06:50 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:06:50 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:06:50
2025-07-08 14:06:50 - fopen() successful.
2025-07-08 14:06:50 - fputcsv() successful. Bytes written: 100
2025-07-08 14:06:50 - fclose() successful.
2025-07-08 14:06:50 - Sending response: success='1', message='Log successful'
2025-07-08 14:07:18 - --- Request Started ---
2025-07-08 14:07:18 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:07:18 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:07:18 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:07:18
2025-07-08 14:07:18 - fopen() successful.
2025-07-08 14:07:18 - fputcsv() successful. Bytes written: 100
2025-07-08 14:07:18 - fclose() successful.
2025-07-08 14:07:18 - Sending response: success='1', message='Log successful'
2025-07-08 14:07:46 - --- Request Started ---
2025-07-08 14:07:46 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:07:46 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:07:46 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:07:46
2025-07-08 14:07:46 - fopen() successful.
2025-07-08 14:07:46 - fputcsv() successful. Bytes written: 100
2025-07-08 14:07:46 - fclose() successful.
2025-07-08 14:07:46 - Sending response: success='1', message='Log successful'
2025-07-08 14:08:13 - --- Request Started ---
2025-07-08 14:08:13 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:08:13 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:08:13 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:08:13
2025-07-08 14:08:13 - fopen() successful.
2025-07-08 14:08:13 - fputcsv() successful. Bytes written: 100
2025-07-08 14:08:13 - fclose() successful.
2025-07-08 14:08:13 - Sending response: success='1', message='Log successful'
2025-07-08 14:08:43 - --- Request Started ---
2025-07-08 14:08:43 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:08:43 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:08:43 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:08:43
2025-07-08 14:08:43 - fopen() successful.
2025-07-08 14:08:43 - fputcsv() successful. Bytes written: 100
2025-07-08 14:08:43 - fclose() successful.
2025-07-08 14:08:43 - Sending response: success='1', message='Log successful'
2025-07-08 14:09:17 - --- Request Started ---
2025-07-08 14:09:17 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:09:17 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:09:17 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:09:17
2025-07-08 14:09:17 - fopen() successful.
2025-07-08 14:09:17 - fputcsv() successful. Bytes written: 100
2025-07-08 14:09:17 - fclose() successful.
2025-07-08 14:09:17 - Sending response: success='1', message='Log successful'
2025-07-08 14:09:46 - --- Request Started ---
2025-07-08 14:09:46 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:09:46 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:09:46 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:09:46
2025-07-08 14:09:46 - fopen() successful.
2025-07-08 14:09:46 - fputcsv() successful. Bytes written: 100
2025-07-08 14:09:46 - fclose() successful.
2025-07-08 14:09:46 - Sending response: success='1', message='Log successful'
2025-07-08 14:10:22 - --- Request Started ---
2025-07-08 14:10:22 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:10:22 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:10:22 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:10:22
2025-07-08 14:10:22 - fopen() successful.
2025-07-08 14:10:22 - fputcsv() successful. Bytes written: 100
2025-07-08 14:10:22 - fclose() successful.
2025-07-08 14:10:22 - Sending response: success='1', message='Log successful'
2025-07-08 14:11:14 - --- Request Started ---
2025-07-08 14:11:14 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:11:14 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:11:14 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:11:14
2025-07-08 14:11:14 - fopen() successful.
2025-07-08 14:11:14 - fputcsv() successful. Bytes written: 100
2025-07-08 14:11:14 - fclose() successful.
2025-07-08 14:11:14 - Sending response: success='1', message='Log successful'
2025-07-08 14:12:01 - --- Request Started ---
2025-07-08 14:12:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:12:01 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:12:01 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:12:01
2025-07-08 14:12:01 - fopen() successful.
2025-07-08 14:12:01 - fputcsv() successful. Bytes written: 100
2025-07-08 14:12:01 - fclose() successful.
2025-07-08 14:12:01 - Sending response: success='1', message='Log successful'
2025-07-08 14:13:31 - --- Request Started ---
2025-07-08 14:13:31 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:13:31 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:13:31 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:13:31
2025-07-08 14:13:31 - fopen() successful.
2025-07-08 14:13:31 - fputcsv() successful. Bytes written: 100
2025-07-08 14:13:31 - fclose() successful.
2025-07-08 14:13:31 - Sending response: success='1', message='Log successful'
2025-07-08 14:14:01 - --- Request Started ---
2025-07-08 14:14:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:14:01 - Received data: {"telegramUserId":7900266169,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:14:01 - Prepared data for CSV: 7900266169,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:14:01
2025-07-08 14:14:01 - fopen() successful.
2025-07-08 14:14:01 - fputcsv() successful. Bytes written: 100
2025-07-08 14:14:01 - fclose() successful.
2025-07-08 14:14:01 - Sending response: success='1', message='Log successful'
2025-07-08 14:16:03 - --- Request Started ---
2025-07-08 14:16:03 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:16:03 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:16:03 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:16:03
2025-07-08 14:16:03 - fopen() successful.
2025-07-08 14:16:03 - fputcsv() successful. Bytes written: 100
2025-07-08 14:16:03 - fclose() successful.
2025-07-08 14:16:03 - Sending response: success='1', message='Log successful'
2025-07-08 14:16:38 - --- Request Started ---
2025-07-08 14:16:38 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:16:38 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:16:38 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:16:38
2025-07-08 14:16:38 - fopen() successful.
2025-07-08 14:16:38 - fputcsv() successful. Bytes written: 100
2025-07-08 14:16:38 - fclose() successful.
2025-07-08 14:16:38 - Sending response: success='1', message='Log successful'
2025-07-08 14:17:21 - --- Request Started ---
2025-07-08 14:17:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:17:21 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:17:21 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:17:21
2025-07-08 14:17:21 - fopen() successful.
2025-07-08 14:17:21 - fputcsv() successful. Bytes written: 100
2025-07-08 14:17:21 - fclose() successful.
2025-07-08 14:17:21 - Sending response: success='1', message='Log successful'
2025-07-08 14:18:13 - --- Request Started ---
2025-07-08 14:18:13 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:18:13 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:18:13 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:18:13
2025-07-08 14:18:13 - fopen() successful.
2025-07-08 14:18:13 - fputcsv() successful. Bytes written: 100
2025-07-08 14:18:13 - fclose() successful.
2025-07-08 14:18:13 - Sending response: success='1', message='Log successful'
2025-07-08 14:18:57 - --- Request Started ---
2025-07-08 14:18:57 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:18:57 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:18:57 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:18:57
2025-07-08 14:18:57 - fopen() successful.
2025-07-08 14:18:57 - fputcsv() successful. Bytes written: 100
2025-07-08 14:18:57 - fclose() successful.
2025-07-08 14:18:57 - Sending response: success='1', message='Log successful'
2025-07-08 14:19:34 - --- Request Started ---
2025-07-08 14:19:34 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:19:34 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:19:34 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:19:34
2025-07-08 14:19:34 - fopen() successful.
2025-07-08 14:19:34 - fputcsv() successful. Bytes written: 100
2025-07-08 14:19:34 - fclose() successful.
2025-07-08 14:19:34 - Sending response: success='1', message='Log successful'
2025-07-08 14:20:31 - --- Request Started ---
2025-07-08 14:20:31 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:20:31 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:20:31 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:20:31
2025-07-08 14:20:31 - fopen() successful.
2025-07-08 14:20:31 - fputcsv() successful. Bytes written: 100
2025-07-08 14:20:31 - fclose() successful.
2025-07-08 14:20:31 - Sending response: success='1', message='Log successful'
2025-07-08 14:21:01 - --- Request Started ---
2025-07-08 14:21:01 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:21:01 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:21:01 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:21:01
2025-07-08 14:21:01 - fopen() successful.
2025-07-08 14:21:01 - fputcsv() successful. Bytes written: 100
2025-07-08 14:21:01 - fclose() successful.
2025-07-08 14:21:01 - Sending response: success='1', message='Log successful'
2025-07-08 14:22:21 - --- Request Started ---
2025-07-08 14:22:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:22:21 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:22:21 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:22:21
2025-07-08 14:22:21 - fopen() successful.
2025-07-08 14:22:21 - fputcsv() successful. Bytes written: 100
2025-07-08 14:22:21 - fclose() successful.
2025-07-08 14:22:21 - Sending response: success='1', message='Log successful'
2025-07-08 14:25:32 - --- Request Started ---
2025-07-08 14:25:32 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:25:32 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:25:32 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:25:32
2025-07-08 14:25:32 - fopen() successful.
2025-07-08 14:25:32 - fputcsv() successful. Bytes written: 100
2025-07-08 14:25:32 - fclose() successful.
2025-07-08 14:25:32 - Sending response: success='1', message='Log successful'
2025-07-08 14:26:11 - --- Request Started ---
2025-07-08 14:26:11 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:26:11 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:26:11 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:26:11
2025-07-08 14:26:11 - fopen() successful.
2025-07-08 14:26:11 - fputcsv() successful. Bytes written: 100
2025-07-08 14:26:11 - fclose() successful.
2025-07-08 14:26:11 - Sending response: success='1', message='Log successful'
2025-07-08 14:27:23 - --- Request Started ---
2025-07-08 14:27:23 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:27:23 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:27:23 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:27:23
2025-07-08 14:27:23 - fopen() successful.
2025-07-08 14:27:23 - fputcsv() successful. Bytes written: 100
2025-07-08 14:27:23 - fclose() successful.
2025-07-08 14:27:23 - Sending response: success='1', message='Log successful'
2025-07-08 14:27:51 - --- Request Started ---
2025-07-08 14:27:51 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:27:51 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:27:51 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:27:51
2025-07-08 14:27:51 - fopen() successful.
2025-07-08 14:27:51 - fputcsv() successful. Bytes written: 100
2025-07-08 14:27:51 - fclose() successful.
2025-07-08 14:27:51 - Sending response: success='1', message='Log successful'
2025-07-08 14:28:32 - --- Request Started ---
2025-07-08 14:28:32 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:28:32 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:28:32 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:28:32
2025-07-08 14:28:32 - fopen() successful.
2025-07-08 14:28:32 - fputcsv() successful. Bytes written: 100
2025-07-08 14:28:32 - fclose() successful.
2025-07-08 14:28:32 - Sending response: success='1', message='Log successful'
2025-07-08 14:29:11 - --- Request Started ---
2025-07-08 14:29:11 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:29:11 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:29:11 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:29:11
2025-07-08 14:29:11 - fopen() successful.
2025-07-08 14:29:11 - fputcsv() successful. Bytes written: 100
2025-07-08 14:29:11 - fclose() successful.
2025-07-08 14:29:11 - Sending response: success='1', message='Log successful'
2025-07-08 14:30:31 - --- Request Started ---
2025-07-08 14:30:31 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:30:31 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:30:31 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:30:31
2025-07-08 14:30:31 - fopen() successful.
2025-07-08 14:30:31 - fputcsv() successful. Bytes written: 100
2025-07-08 14:30:31 - fclose() successful.
2025-07-08 14:30:31 - Sending response: success='1', message='Log successful'
2025-07-08 14:31:04 - --- Request Started ---
2025-07-08 14:31:04 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:31:04 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:31:04 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:31:04
2025-07-08 14:31:04 - fopen() successful.
2025-07-08 14:31:04 - fputcsv() successful. Bytes written: 100
2025-07-08 14:31:04 - fclose() successful.
2025-07-08 14:31:04 - Sending response: success='1', message='Log successful'
2025-07-08 14:31:52 - --- Request Started ---
2025-07-08 14:31:52 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:31:52 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:31:52 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:31:52
2025-07-08 14:31:52 - fopen() successful.
2025-07-08 14:31:52 - fputcsv() successful. Bytes written: 100
2025-07-08 14:31:52 - fclose() successful.
2025-07-08 14:31:52 - Sending response: success='1', message='Log successful'
2025-07-08 14:32:28 - --- Request Started ---
2025-07-08 14:32:28 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:32:28 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:32:28 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:32:28
2025-07-08 14:32:28 - fopen() successful.
2025-07-08 14:32:28 - fputcsv() successful. Bytes written: 100
2025-07-08 14:32:28 - fclose() successful.
2025-07-08 14:32:28 - Sending response: success='1', message='Log successful'
2025-07-08 14:33:02 - --- Request Started ---
2025-07-08 14:33:02 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:33:02 - Received data: {"telegramUserId":7941411616,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:33:02 - Prepared data for CSV: 7941411616,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:33:02
2025-07-08 14:33:02 - fopen() successful.
2025-07-08 14:33:02 - fputcsv() successful. Bytes written: 100
2025-07-08 14:33:02 - fclose() successful.
2025-07-08 14:33:02 - Sending response: success='1', message='Log successful'
2025-07-08 14:34:19 - --- Request Started ---
2025-07-08 14:34:19 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:34:19 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:34:19 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:34:19
2025-07-08 14:34:19 - fopen() successful.
2025-07-08 14:34:19 - fputcsv() successful. Bytes written: 100
2025-07-08 14:34:19 - fclose() successful.
2025-07-08 14:34:19 - Sending response: success='1', message='Log successful'
2025-07-08 14:34:46 - --- Request Started ---
2025-07-08 14:34:46 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:34:46 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:34:46 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:34:46
2025-07-08 14:34:46 - fopen() successful.
2025-07-08 14:34:46 - fputcsv() successful. Bytes written: 100
2025-07-08 14:34:46 - fclose() successful.
2025-07-08 14:34:46 - Sending response: success='1', message='Log successful'
2025-07-08 14:35:15 - --- Request Started ---
2025-07-08 14:35:15 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:35:15 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:35:15 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:35:15
2025-07-08 14:35:15 - fopen() successful.
2025-07-08 14:35:15 - fputcsv() successful. Bytes written: 100
2025-07-08 14:35:15 - fclose() successful.
2025-07-08 14:35:15 - Sending response: success='1', message='Log successful'
2025-07-08 14:35:41 - --- Request Started ---
2025-07-08 14:35:41 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:35:41 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:35:41 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:35:41
2025-07-08 14:35:41 - fopen() successful.
2025-07-08 14:35:41 - fputcsv() successful. Bytes written: 100
2025-07-08 14:35:41 - fclose() successful.
2025-07-08 14:35:41 - Sending response: success='1', message='Log successful'
2025-07-08 14:36:07 - --- Request Started ---
2025-07-08 14:36:07 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:36:07 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:36:07 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:36:07
2025-07-08 14:36:07 - fopen() successful.
2025-07-08 14:36:07 - fputcsv() successful. Bytes written: 100
2025-07-08 14:36:07 - fclose() successful.
2025-07-08 14:36:07 - Sending response: success='1', message='Log successful'
2025-07-08 14:36:34 - --- Request Started ---
2025-07-08 14:36:34 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:36:34 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:36:34 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:36:34
2025-07-08 14:36:34 - fopen() successful.
2025-07-08 14:36:34 - fputcsv() successful. Bytes written: 100
2025-07-08 14:36:34 - fclose() successful.
2025-07-08 14:36:34 - Sending response: success='1', message='Log successful'
2025-07-08 14:37:12 - --- Request Started ---
2025-07-08 14:37:12 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:37:12 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:37:12 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:37:12
2025-07-08 14:37:12 - fopen() successful.
2025-07-08 14:37:12 - fputcsv() successful. Bytes written: 100
2025-07-08 14:37:12 - fclose() successful.
2025-07-08 14:37:12 - Sending response: success='1', message='Log successful'
2025-07-08 14:37:44 - --- Request Started ---
2025-07-08 14:37:44 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:37:44 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:37:44 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:37:44
2025-07-08 14:37:44 - fopen() successful.
2025-07-08 14:37:44 - fputcsv() successful. Bytes written: 100
2025-07-08 14:37:44 - fclose() successful.
2025-07-08 14:37:44 - Sending response: success='1', message='Log successful'
2025-07-08 14:38:18 - --- Request Started ---
2025-07-08 14:38:18 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:38:18 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:38:18 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:38:18
2025-07-08 14:38:18 - fopen() successful.
2025-07-08 14:38:18 - fputcsv() successful. Bytes written: 100
2025-07-08 14:38:18 - fclose() successful.
2025-07-08 14:38:18 - Sending response: success='1', message='Log successful'
2025-07-08 14:39:08 - --- Request Started ---
2025-07-08 14:39:08 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:39:08 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:39:08 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:39:08
2025-07-08 14:39:08 - fopen() successful.
2025-07-08 14:39:08 - fputcsv() successful. Bytes written: 100
2025-07-08 14:39:08 - fclose() successful.
2025-07-08 14:39:08 - Sending response: success='1', message='Log successful'
2025-07-08 14:39:53 - --- Request Started ---
2025-07-08 14:39:53 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:39:53 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:39:53 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:39:53
2025-07-08 14:39:53 - fopen() successful.
2025-07-08 14:39:53 - fputcsv() successful. Bytes written: 100
2025-07-08 14:39:53 - fclose() successful.
2025-07-08 14:39:53 - Sending response: success='1', message='Log successful'
2025-07-08 14:40:20 - --- Request Started ---
2025-07-08 14:40:20 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:40:20 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:40:20 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:40:20
2025-07-08 14:40:20 - fopen() successful.
2025-07-08 14:40:20 - fputcsv() successful. Bytes written: 100
2025-07-08 14:40:20 - fclose() successful.
2025-07-08 14:40:20 - Sending response: success='1', message='Log successful'
2025-07-08 14:40:48 - --- Request Started ---
2025-07-08 14:40:48 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:40:48 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:40:48 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:40:48
2025-07-08 14:40:48 - fopen() successful.
2025-07-08 14:40:48 - fputcsv() successful. Bytes written: 100
2025-07-08 14:40:48 - fclose() successful.
2025-07-08 14:40:48 - Sending response: success='1', message='Log successful'
2025-07-08 14:41:21 - --- Request Started ---
2025-07-08 14:41:21 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:41:21 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:41:21 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:41:21
2025-07-08 14:41:21 - fopen() successful.
2025-07-08 14:41:21 - fputcsv() successful. Bytes written: 100
2025-07-08 14:41:21 - fclose() successful.
2025-07-08 14:41:21 - Sending response: success='1', message='Log successful'
2025-07-08 14:41:48 - --- Request Started ---
2025-07-08 14:41:48 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:41:48 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:41:48 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:41:48
2025-07-08 14:41:48 - fopen() successful.
2025-07-08 14:41:48 - fputcsv() successful. Bytes written: 100
2025-07-08 14:41:48 - fclose() successful.
2025-07-08 14:41:48 - Sending response: success='1', message='Log successful'
2025-07-08 14:42:17 - --- Request Started ---
2025-07-08 14:42:17 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:42:17 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:42:17 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:42:17
2025-07-08 14:42:17 - fopen() successful.
2025-07-08 14:42:17 - fputcsv() successful. Bytes written: 100
2025-07-08 14:42:17 - fclose() successful.
2025-07-08 14:42:17 - Sending response: success='1', message='Log successful'
2025-07-08 14:42:44 - --- Request Started ---
2025-07-08 14:42:44 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:42:44 - Received data: {"telegramUserId":7752174100,"deviceType":"Linux armv7l","platform":"Linux armv7l","screenResolution":"412x915","deviceManufacturer":"Google Inc."}
2025-07-08 14:42:44 - Prepared data for CSV: 7752174100,102.218.51.115,Linux armv7l,Linux armv7l,412x915,Google Inc.,2025-07-08 14:42:44
2025-07-08 14:42:44 - fopen() successful.
2025-07-08 14:42:44 - fputcsv() successful. Bytes written: 100
2025-07-08 14:42:44 - fclose() successful.
2025-07-08 14:42:44 - Sending response: success='1', message='Log successful'
2025-07-08 14:47:04 - --- Request Started ---
2025-07-08 14:47:04 - Log file path is: /var/www/html/test3/api/../database/richadds_success_log.csv
2025-07-08 14:47:04 - Received data: {"telegramUserId":5731750628,"deviceType":"Linux aarch64","platform":"Linux aarch64","screenResolution":"360x820","deviceManufacturer":"Google Inc."}
2025-07-08 14:47:04 - Prepared data for CSV: 5731750628,176.59.74.168,Linux aarch64,Linux aarch64,360x820,Google Inc.,2025-07-08 14:47:04
2025-07-08 14:47:04 - fopen() successful.
2025-07-08 14:47:04 - fputcsv() successful. Bytes written: 101
2025-07-08 14:47:04 - fclose() successful.
2025-07-08 14:47:04 - Sending response: success='1', message='Log successful'
