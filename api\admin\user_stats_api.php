<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../db_mock.php';

$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$period = isset($_GET['period']) ? $_GET['period'] : 'last_7_days';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;

if ($userId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid user ID']);
    exit;
}

function getUserActivityStats($userId, $period, $dateFrom, $dateTo) {
    $logFile = __DIR__ . '/../../database/user_clicks_log.json';
    if (!file_exists($logFile)) {
        return [];
    }

    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $stats = [];
    $userClicks = [];

    // 1. Фильтруем все клики для нужного пользователя
    foreach ($lines as $line) {
        $data = json_decode($line, true);
        if ($data && isset($data['user_id']) && $data['user_id'] == $userId) {
            $userClicks[] = $data;
        }
    }

    // 2. Инициализируем и заполняем статистику
    if ($period === 'last_24_hours') {
        for ($i = 0; $i < 24; $i++) {
            $stats[date('H:00', strtotime("-$i hours"))] = 0;
        }
        $startTime = strtotime('-24 hours');
        foreach ($userClicks as $click) {
            if ($click['timestamp'] >= $startTime) {
                $hourKey = date('H:00', $click['timestamp']);
                if (isset($stats[$hourKey])) {
                    $stats[$hourKey]++;
                }
            }
        }
        krsort($stats);
    } else { // Дневная статистика
        $startDate = null;
        $endDate = null;

        if ($period === 'last_7_days') {
            $startDate = new DateTime('-6 days');
            $endDate = new DateTime('today');
        } elseif ($period === 'custom_date' && !empty($dateFrom) && !empty($dateTo)) {
            $startDate = new DateTime($dateFrom);
            $endDate = new DateTime($dateTo);
        }

        if ($startDate && $endDate) {
            $currentDate = clone $startDate;
            while ($currentDate <= $endDate) {
                $stats[$currentDate->format('Y-m-d')] = 0;
                $currentDate->modify('+1 day');
            }

            foreach ($userClicks as $click) {
                $clickDate = date('Y-m-d', $click['timestamp']);
                if (isset($stats[$clickDate])) {
                    $stats[$clickDate]++;
                }
            }
        }
    }

    return $stats;
}

$data = getUserActivityStats($userId, $period, $dateFrom, $dateTo);

echo json_encode(['success' => true, 'data' => $data]);
