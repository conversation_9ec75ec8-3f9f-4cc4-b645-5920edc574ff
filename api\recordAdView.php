<?php
/**
 * api/recordAdView.php
 * API эндпоинт для записи просмотра рекламы и начисления награды (включая реф. бонус).
 */

// Включаем логирование для этого скрипта тоже (если нужно)
// ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }
if (!(@require_once __DIR__ . '/security.php')) { http_response_code(500); error_log('FATAL: security.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: SEC']); exit; }
// --- Конец проверки зависимостей ---

/**
 * Логирование рекламных запросов для статистики
 */
function logAdRequest($userId, $adType, $status, $clientIp, $userAgent = '', $country = '', $additionalData = []) {
    $logFile = __DIR__ . '/ad_requests.log';
    $timestamp = time();
    // ИЗМЕНЕНО: Используем UTC время для всех логов
    $date = gmdate('Y-m-d H:i:s', $timestamp);

    // Определяем страну по IP (простая заглушка, можно интегрировать с GeoIP)
    if (empty($country)) {
        $country = getCountryByIP($clientIp);
    }

    $logEntry = [
        'timestamp' => $timestamp,
        'date' => $date,
        'user_id' => $userId,
        'ad_type' => $adType,
        'status' => $status, // 'request', 'success', 'empty', 'error', 'limit_exceeded', 'click', 'ad_shown_no_reward', 'blocked_user'
        'ip' => $clientIp,
        'user_agent' => $userAgent,
        'country' => $country
    ];

    // Добавляем дополнительные данные если есть
    if (!empty($additionalData)) {
        $logEntry = array_merge($logEntry, $additionalData);
    }

    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Определение страны по IP
 * Использует кэширование для оптимизации
 */
function getCountryByIP($ip) {
    // Кэш для IP адресов
    static $ipCache = [];

    if (isset($ipCache[$ip])) {
        return $ipCache[$ip];
    }

    // Проверяем локальные/приватные IP
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        $ipCache[$ip] = 'Local';
        return 'Local';
    }

    // Расширенная проверка для российских IP диапазонов
    $russianRanges = [
        '/^(185\.250\.|95\.25\.|46\.17\.|78\.85\.|91\.200\.|91\.201\.|91\.202\.|91\.203\.)/',
        '/^(176\.59\.|176\.60\.|176\.61\.|176\.62\.|176\.63\.|176\.64\.|176\.65\.|176\.66\.)/',
        '/^(188\.162\.|188\.163\.|188\.164\.|188\.165\.|188\.166\.|188\.167\.|188\.168\.|188\.169\.)/',
        '/^(37\.139\.|37\.140\.|37\.141\.|37\.142\.|37\.143\.|37\.144\.|37\.145\.|37\.146\.)/',
        '/^(5\.34\.|5\.35\.|5\.36\.|5\.37\.|5\.38\.|5\.39\.|5\.40\.|5\.41\.)/',
        '/^(77\.88\.|77\.89\.|77\.90\.|77\.91\.|77\.92\.|77\.93\.|77\.94\.|77\.95\.)/',
        '/^(213\.180\.|213\.181\.|213\.182\.|213\.183\.|213\.184\.|213\.185\.|213\.186\.|213\.187\.)/',
        '/^(217\.69\.|217\.70\.|217\.71\.|217\.72\.|217\.73\.|217\.74\.|217\.75\.|217\.76\.)/'
    ];

    foreach ($russianRanges as $range) {
        if (preg_match($range, $ip)) {
            $ipCache[$ip] = 'RU';
            return 'RU';
        }
    }

    // Проверка для американских IP диапазонов
    $americanRanges = [
        '/^(8\.8\.|8\.34\.|1\.1\.|1\.0\.)/', // Google DNS, Cloudflare
        '/^(208\.67\.|208\.68\.|4\.2\.|4\.4\.)/', // OpenDNS, Level3
        '/^(173\.252\.|173\.253\.|31\.13\.|69\.63\.)/', // Facebook
        '/^(74\.125\.|172\.217\.|216\.58\.|142\.250\.)/', // Google
        '/^(52\.|54\.|3\.|13\.|18\.|34\.|35\.)/', // Amazon AWS
        '/^(104\.16\.|104\.17\.|104\.18\.|104\.19\.)/' // Cloudflare
    ];

    foreach ($americanRanges as $range) {
        if (preg_match($range, $ip)) {
            $ipCache[$ip] = 'US';
            return 'US';
        }
    }

    // Проверка для других стран
    $otherCountries = [
        'DE' => ['/^(46\.4\.|85\.25\.|217\.160\.|62\.75\.)/', '/^(195\.93\.|212\.227\.|217\.5\.|80\.67\.)/'],
        'FR' => ['/^(213\.186\.|80\.12\.|82\.64\.|90\.84\.)/', '/^(193\.252\.|212\.27\.|217\.128\.|80\.236\.)/'],
        'GB' => ['/^(212\.58\.|81\.2\.|86\.1\.|194\.74\.)/', '/^(195\.92\.|212\.219\.|217\.169\.|80\.68\.)/'],
        'CN' => ['/^(61\.135\.|123\.125\.|220\.181\.|202\.108\.)/', '/^(119\.75\.|180\.149\.|183\.232\.|14\.215\.)/'],
        'UA' => ['/^(91\.224\.|91\.225\.|91\.226\.|91\.227\.)/', '/^(178\.137\.|185\.25\.|188\.163\.|195\.138\.)/'],
        'BY' => ['/^(178\.172\.|178\.173\.|178\.174\.|178\.175\.)/', '/^(93\.84\.|178\.124\.|212\.98\.|217\.21\.)/'],
        'KZ' => ['/^(92\.46\.|92\.47\.|92\.48\.|92\.49\.)/', '/^(195\.210\.|212\.154\.|217\.174\.|80\.89\.)/']
    ];

    foreach ($otherCountries as $countryCode => $ranges) {
        foreach ($ranges as $range) {
            if (preg_match($range, $ip)) {
                $ipCache[$ip] = $countryCode;
                return $countryCode;
            }
        }
    }

    // Попробуем API для точного определения страны
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 3,
                'user_agent' => 'UniQPaid/1.0'
            ]
        ]);

        // Используем ip-api.com (бесплатный, без ключа)
        $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['countryCode']) && !empty($data['countryCode'])) {
                $country = strtoupper($data['countryCode']);
                $ipCache[$ip] = $country;
                return $country;
            }
        }
    } catch (Exception $e) {
        error_log("GeoIP API error: " . $e->getMessage());
    }

    $ipCache[$ip] = 'Unknown';
    return 'Unknown';
}

// 1. Получение IP для логирования (без проверки лимитов)
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
// ИСПРАВЛЕНИЕ: Убираем проверку IP rate limiting для кеша
// if (!checkIpRateLimit($clientIp)) {
//     error_log("recordAdView WARNING: IP {$clientIp} заблокирован из-за превышения лимита запросов");
//     http_response_code(429);
//     echo json_encode(['error' => 'Слишком много запросов. Попробуйте позже']);
//     exit;
// }

// 2. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
error_log("recordAdView DEBUG: Получен JSON: " . $inputJSON);

$input = json_decode($inputJSON, true);
error_log("recordAdView DEBUG: Декодированные данные: " . print_r($input, true));

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    error_log("recordAdView ERROR: Нет данных или неверный JSON");

    // Логируем ошибочный запрос
    logAdRequest(0, $input['adType'] ?? 'unknown', 'error', $clientIp, $userAgent);

    http_response_code(400); echo json_encode(['error' => 'Ошибка запроса: Нет данных']); exit;
}
$initData = $input['initData'];
error_log("recordAdView INFO: Получен initData (длина: " . strlen($initData) . ") от IP: {$clientIp}");

// 3. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    // Ошибка уже залогирована внутри validateTelegramInitData

    // Логируем неудачную валидацию
    logAdRequest(0, $input['adType'] ?? 'unknown', 'error', $clientIp, $userAgent);

    http_response_code(403); echo json_encode(['error' => 'Ошибка: Неверные данные']); exit;
}
$userId = intval($validatedData['user']['id']);
error_log("recordAdView INFO: initData валидирован для user $userId от IP: {$clientIp}");

// Логируем запрос рекламы
$adType = $input['adType'] ?? 'default';
logAdRequest($userId, $adType, 'request', $clientIp, $userAgent);

// 4. Загрузка данных ВСЕХ пользователей
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("recordAdView ERROR: loadUserData вернул не массив."); http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: LD1']); exit;
}
error_log("recordAdView INFO: Данные пользователей загружены для обработки награды user $userId.");

// 4. Проверка лимитов и начисление основной награды
$adType = $input['adType'] ?? 'default';
$rewardAmount = 1; // Минимальное значение по умолчанию для обратной совместимости

if ($adType === 'native_banner' && defined('AD_REWARD_NATIVE_BANNER')) {
    $rewardAmount = AD_REWARD_NATIVE_BANNER;
} elseif ($adType === 'interstitial' && defined('AD_REWARD_INTERSTITIAL')) {
    $rewardAmount = AD_REWARD_INTERSTITIAL;
} elseif ($adType === 'rewarded_video' && defined('AD_REWARD_REWARDED_VIDEO')) {
    $rewardAmount = AD_REWARD_REWARDED_VIDEO;
}

// Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("recordAdView WARNING: Попытка начисления монет заблокированному пользователю $userId");

    // Логируем попытку заблокированного пользователя
    logAdRequest($userId, $adType, 'blocked_user', $clientIp, $userAgent);

    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// Проверяем лимит просмотров рекламы по типу
if (!checkAdViewLimitByType($userId, $adType, $userData)) {
    error_log("recordAdView WARNING: Превышен лимит просмотров рекламы типа $adType для пользователя $userId");

    // Логируем превышение лимита
    logAdRequest($userId, $adType, 'limit_exceeded', $clientIp, $userAgent);

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, "ad_view_limit_exceeded_type_{$adType}");

    http_response_code(429);
    echo json_encode(['error' => "Превышен лимит просмотров рекламы типа '$adType'. Попробуйте позже"]);
    exit;
}

// Также проверяем общий лимит для дополнительной защиты
if (!checkAdViewLimit($userId, $userData)) {
    error_log("recordAdView WARNING: Превышен общий лимит просмотров рекламы для пользователя $userId");

    // Логируем превышение общего лимита
    logAdRequest($userId, $adType, 'general_limit_exceeded', $clientIp, $userAgent);

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'ad_view_general_limit_exceeded');

    http_response_code(429);
    echo json_encode(['error' => 'Превышен общий лимит просмотров рекламы. Попробуйте позже']);
    exit;
}

// Логируем событие просмотра рекламы
logAuditEvent('ad_view', $userId, ['reward' => $rewardAmount, 'type' => $input['adType'] ?? 'unknown']);

// Начисляем награду
$newBalanceUser = increaseUserBalance($userId, $rewardAmount, $userData);
if ($newBalanceUser === false) {
    // Ошибка залогирована в increaseUserBalance

    // Логируем ошибку начисления
    logAdRequest($userId, $adType, 'error', $clientIp, $userAgent);

    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: Не удалось обновить баланс']);
    exit;
}

// Обновляем общую сумму заработанных монет
if (!isset($userData[$userId]['total_earned'])) {
    $userData[$userId]['total_earned'] = $rewardAmount;
} else {
    $userData[$userId]['total_earned'] += $rewardAmount;
}

// Обновляем лог просмотров рекламы для статистики админ-панели
if (!isset($userData[$userId]['ad_views_log'])) {
    $userData[$userId]['ad_views_log'] = [];
}
$userData[$userId]['ad_views_log'][] = time();

    // Логируем клик для статистики
    $logFile = __DIR__ . '/../database/user_clicks_log.json';
    $logEntry = json_encode(['user_id' => $userId, 'timestamp' => time(), 'reward_amount' => $rewardAmount]);
    file_put_contents($logFile, $logEntry . "\n", FILE_APPEND);

error_log("recordAdView INFO: Основная награда $rewardAmount начислена user $userId. Новый баланс: $newBalanceUser. Всего заработано: {$userData[$userId]['total_earned']}. Просмотров рекламы: " . count($userData[$userId]['ad_views_log']));

// 5. Начисление реферального бонуса
$referrerId = getUserReferrerId($userId, $userData);
if ($referrerId !== null) {
    error_log("recordAdView INFO: Найден реферер $referrerId для user $userId.");
    // Проверяем, существует ли реферер в данных (мог быть удален)
    if (isset($userData[$referrerId])) {
        $bonusPercent = defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT : 0.10;
        $bonusAmount = floor($rewardAmount * $bonusPercent); // Округляем вниз

        if ($bonusAmount > 0) {
            $newBalanceReferrer = increaseUserBalance($referrerId, $bonusAmount, $userData);
            if ($newBalanceReferrer !== false) {
                // Обновляем статистику реферальных начислений (ТОЛЬКО бонусы от рефералов)
                if (!isset($userData[$referrerId]['referral_earnings'])) {
                    $userData[$referrerId]['referral_earnings'] = 0;
                }
                $userData[$referrerId]['referral_earnings'] += $bonusAmount;

                error_log("recordAdView INFO: Успешно начислен реф. бонус $bonusAmount пользователю $referrerId. Новый баланс реферера: $newBalanceReferrer, Всего заработано на рефералах: " . $userData[$referrerId]['referral_earnings']);
            } else {
                // Ошибка уже залогирована в increaseUserBalance
                error_log("recordAdView ERROR: Не удалось начислить реф. бонус $bonusAmount пользователю $referrerId (возможно, из-за ошибки в increaseUserBalance).");
                // Не прерываем выполнение, т.к. основная награда начислена
            }
        } else {
            error_log("recordAdView INFO: Реф. бонус для $referrerId равен 0 (награда: $rewardAmount, процент: $bonusPercent).");
        }
    } else {
         error_log("recordAdView WARNING: Реферер $referrerId для пользователя $userId не найден в актуальных данных (возможно, был удален).");
    }
}

// 6. Сохранение ВСЕХ измененных данных
if (!saveUserData($userData)) {
    error_log("recordAdView CRITICAL ERROR: Не удалось сохранить данные после начислений для $userId (и возможно реферера $referrerId)");
    http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: Не удалось сохранить данные']); exit;
}
error_log("recordAdView INFO: Все данные успешно сохранены после обработки награды для $userId.");

// 7. Логируем успешный показ рекламы
logAdRequest($userId, $adType, 'success', $clientIp, $userAgent);

// 8. Успешный ответ с полными данными для ЕДИНОЙ СИСТЕМЫ
http_response_code(200); // OK
echo json_encode([
    'success' => true,
    'newBalance' => $newBalanceUser,
    'reward' => $rewardAmount,
    'adType' => $adType,
    'timestamp' => time(),
    'message' => "Награда успешно начислена"
]);
error_log("recordAdView INFO: Успешный ответ отправлен для user $userId с наградой $rewardAmount.");
exit;
?>