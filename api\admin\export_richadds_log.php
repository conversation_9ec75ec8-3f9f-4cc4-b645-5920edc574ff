<?php
// Проверка авторизации администратора
session_start();

// Подключаем функции авторизации
require_once __DIR__ . '/auth.php';

if (!isAuthenticated()) {
    http_response_code(403);
    exit('Access denied');
}

$logFile = __DIR__ . '/../../database/richadds_success_log.csv';

if (!file_exists($logFile)) {
    http_response_code(404);
    exit('Log file not found');
}

// Получаем параметры фильтрации и тип экспорта
$search = trim($_GET['search'] ?? '');
$dateFrom = trim($_GET['date_from'] ?? '');
$dateTo = trim($_GET['date_to'] ?? '');
$exportType = $_GET['export_type'] ?? 'auto';

// Определяем логику фильтрации
$hasFilters = !empty($search) || !empty($dateFrom) || !empty($dateTo);

// Если тип экспорта 'all', игнорируем фильтры
if ($exportType === 'all') {
    $hasFilters = false;
    $search = '';
    $dateFrom = '';
    $dateTo = '';
}

$data = array_map('str_getcsv', file($logFile));
$header = array_shift($data);
$totalRecords = count($data);

// Функция для парсинга времени из CSV (формат HH:MM:SS)
if (!function_exists('parseTimeFromCsv')) {
    function parseTimeFromCsv($timeStr) {
        // Время в формате HH:MM:SS, добавляем сегодняшнюю дату для сравнения
        $today = date('Y-m-d');
        return strtotime($today . ' ' . $timeStr);
    }
}

// Функция для преобразования технических названий платформ в читаемый вид
if (!function_exists('formatPlatform')) {
    function formatPlatform($platform) {
        $platformMap = [
            'Linux armv7l' => 'Android (ARM 32-bit)',
            'Linux aarch64' => 'Android (ARM 64-bit)',
            'Linux x86_64' => 'Linux (64-bit)',
            'Linux i686' => 'Linux (32-bit)',
            'Win32' => 'Windows',
            'MacIntel' => 'macOS (Intel)',
            'MacPPC' => 'macOS (PowerPC)',
            'iPhone' => 'iOS (iPhone)',
            'iPad' => 'iOS (iPad)',
            'iPod' => 'iOS (iPod)'
        ];

        return isset($platformMap[$platform]) ? $platformMap[$platform] : $platform;
    }
}

// Применяем фильтрацию только если заданы фильтры
if ($hasFilters) {
    // Фильтрация по поиску
    if (!empty($search)) {
        $data = array_filter($data, function($row) use ($search) {
            return stripos($row[0], $search) !== false || stripos($row[1], $search) !== false;
        });
    }

    // Фильтрация по датам
    if (!empty($dateFrom) || !empty($dateTo)) {
        $data = array_filter($data, function($row) use ($dateFrom, $dateTo) {
            if (count($row) < 7) return true; // Пропускаем некорректные строки

            $timeStr = $row[6]; // Время в последней колонке
            $recordTime = parseTimeFromCsv($timeStr);

            if (!empty($dateFrom)) {
                $fromTimestamp = strtotime($dateFrom . ' 00:00:00');
                if ($recordTime < $fromTimestamp) return false;
            }

            if (!empty($dateTo)) {
                $toTimestamp = strtotime($dateTo . ' 23:59:59');
                if ($recordTime > $toTimestamp) return false;
            }

            return true;
        });
    }
}

// Сортировка по времени (новые записи сначала) - как в таблице
usort($data, function($a, $b) {
    if (count($a) < 7 || count($b) < 7) return 0;

    // Сравниваем время (колонка 6)
    $timeA = $a[6];
    $timeB = $b[6];

    // Преобразуем время в секунды для сравнения
    $secondsA = timeToSecondsExport($timeA);
    $secondsB = timeToSecondsExport($timeB);

    // Сортируем по убыванию (новые сначала)
    return $secondsB - $secondsA;
});

// Функция для преобразования времени HH:MM:SS в секунды (для экспорта)
if (!function_exists('timeToSecondsExport')) {
    function timeToSecondsExport($timeStr) {
        $parts = explode(':', $timeStr);
        if (count($parts) != 3) return 0;

        $hours = (int)$parts[0];
        $minutes = (int)$parts[1];
        $seconds = (int)$parts[2];

        return $hours * 3600 + $minutes * 60 + $seconds;
    }
}

$filteredRecords = count($data);

// Формируем имя файла с информацией о количестве записей
$filenameParts = ['richadds_success_log'];

// Определяем тип экспорта для имени файла
switch ($exportType) {
    case 'all':
        $filenameParts[] = 'all';
        $filenameParts[] = $totalRecords . '_records';
        break;
    case 'filtered':
        $filenameParts[] = 'filtered';
        $filenameParts[] = $filteredRecords . '_of_' . $totalRecords;
        break;
    default:
        // Автоматическое определение (старая логика)
        if ($hasFilters) {
            $filenameParts[] = 'filtered';
            $filenameParts[] = $filteredRecords . '_of_' . $totalRecords;
        } else {
            $filenameParts[] = 'all';
            $filenameParts[] = $totalRecords . '_records';
        }
        break;
}

$filenameParts[] = date('Y-m-d_H-i-s');
$filename = implode('_', $filenameParts) . '.csv';

// Устанавливаем заголовки для скачивания файла
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Открываем поток вывода
$output = fopen('php://output', 'w');

// Записываем заголовки (в нужном порядке согласно требованиям)
$exportHeader = [
    'Telegram User ID',
    'IP Address',
    'Device Type',
    'Platform',
    'Screen Resolution',
    'Device Manufacturer',
    'Time'
];
fputcsv($output, $exportHeader);

// Записываем данные
foreach ($data as $row) {
    if (count($row) >= 7) {
        // Формируем строку в нужном порядке: User ID, IP, Device Type, Platform, Screen Resolution, Device Manufacturer, Time
        $exportRow = [
            $row[0], // Telegram User ID
            $row[1], // IP Address
            $row[2], // Device Type
            formatPlatform($row[3]), // Platform (форматированная)
            $row[4], // Screen Resolution
            $row[5], // Device Manufacturer
            $row[6]  // Time (UTC время в формате HH:MM:SS)
        ];
        fputcsv($output, $exportRow);
    }
}

fclose($output);
exit;
