<?php
// Проверка авторизации администратора
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    exit('Access denied');
}

$logFile = __DIR__ . '/../../database/richadds_success_log.csv';

if (!file_exists($logFile)) {
    http_response_code(404);
    exit('Log file not found');
}

// Получаем параметры фильтрации
$search = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$data = array_map('str_getcsv', file($logFile));
$header = array_shift($data);

// Функция для парсинга времени из CSV (формат HH:MM:SS)
if (!function_exists('parseTimeFromCsv')) {
    function parseTimeFromCsv($timeStr) {
        // Время в формате HH:MM:SS, добавляем сегодняшнюю дату для сравнения
        $today = date('Y-m-d');
        return strtotime($today . ' ' . $timeStr);
    }
}

// Фильтрация по поиску
if ($search) {
    $data = array_filter($data, function($row) use ($search) {
        return stripos($row[0], $search) !== false || stripos($row[1], $search) !== false;
    });
}

// Фильтрация по датам
if ($dateFrom || $dateTo) {
    $data = array_filter($data, function($row) use ($dateFrom, $dateTo) {
        if (count($row) < 7) return true; // Пропускаем некорректные строки
        
        $timeStr = $row[6]; // Время в последней колонке
        $recordTime = parseTimeFromCsv($timeStr);
        
        if ($dateFrom) {
            $fromTimestamp = strtotime($dateFrom . ' 00:00:00');
            if ($recordTime < $fromTimestamp) return false;
        }
        
        if ($dateTo) {
            $toTimestamp = strtotime($dateTo . ' 23:59:59');
            if ($recordTime > $toTimestamp) return false;
        }
        
        return true;
    });
}

// Формируем имя файла с датой и временем
$filename = 'richadds_success_log_' . date('Y-m-d_H-i-s') . '.csv';

// Устанавливаем заголовки для скачивания файла
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Открываем поток вывода
$output = fopen('php://output', 'w');

// Записываем заголовки (в нужном порядке согласно требованиям)
$exportHeader = [
    'Telegram User ID',
    'IP Address',
    'Device Type',
    'Platform',
    'Screen Resolution',
    'Time'
];
fputcsv($output, $exportHeader);

// Записываем данные
foreach ($data as $row) {
    if (count($row) >= 7) {
        // Формируем строку в нужном порядке: User ID, IP, Device Type, Platform, Screen Resolution, Time
        $exportRow = [
            $row[0], // Telegram User ID
            $row[1], // IP Address
            $row[2], // Device Type
            $row[3], // Platform
            $row[4], // Screen Resolution
            $row[6]  // Time (UTC время в формате HH:MM:SS) - пропускаем Device Manufacturer (индекс 5)
        ];
        fputcsv($output, $exportRow);
    }
}

fclose($output);
exit;
